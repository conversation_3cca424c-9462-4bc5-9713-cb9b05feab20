#include "SysConfig.h"

// 外部声明电机实例
extern Motor_t left_motor;
extern Motor_t right_motor;

int main(void)
{
    SYSCFG_DL_init();

    // 简单的电机测试模式 - 注释掉任务系统，直接测试电机
    Motor_Start(); // 初始化电机

    // 简单的电机测试循环
    while (1)
    {
        // 测试电机正转
        Motor_SetSpeed(&left_motor, 50);   // 左电机50%速度正转
        Motor_SetSpeed(&right_motor, 50);  // 右电机50%速度正转

        // 延时3秒
        for(volatile uint32_t i = 0; i < 3000000; i++);

        // 测试电机反转
        Motor_SetSpeed(&left_motor, -50);  // 左电机50%速度反转
        Motor_SetSpeed(&right_motor, -50); // 右电机50%速度反转

        // 延时3秒
        for(volatile uint32_t i = 0; i < 3000000; i++);

        // 停止电机
        Motor_SetSpeed(&left_motor, 0);    // 停止左电机
        Motor_SetSpeed(&right_motor, 0);   // 停止右电机

        // 延时2秒
        for(volatile uint32_t i = 0; i < 2000000; i++);
    }

    /* 原任务系统代码暂时注释，专注电机测试
    Task_Init();
    while (1)
    {
        Task_Start(Sys_GetTick);
    }
    */
}
