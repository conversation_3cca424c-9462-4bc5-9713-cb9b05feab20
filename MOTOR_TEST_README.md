# 电机测试版本修改说明

## 修改目的
将原工程中移植的driver_app.c和driver_app.h与现有代码进行兼容性修改，专注于实现电机的基本转动功能。

## 主要修改内容

### 1. 头文件修改
- **APP/Inc/SysConfig.h**: 将`#include "Motor.h"`改为`#include "driver_app.h"`

### 2. 电机驱动兼容性修改
- **BSP/Inc/driver_app.h**: 
  - 添加了`MOTOR_Def_t`结构体定义，包含PID控制实例
  - 添加了4个电机实例的外部声明
  - 添加了兼容函数声明：`Motor_Start()`, `Motor_GetSpeed()`, `Motor_SetDuty()`

- **BSP/Src/driver_app.c**:
  - 添加了4个`MOTOR_Def_t`类型的电机实例
  - 实现了`Motor_Start()`函数
  - 实现了兼容的`Motor_GetSpeed()`和`Motor_SetDuty()`函数
  - 初始化了PID控制器和编码器地址指针

### 3. 应用层简化修改
- **APP/Src/Task_App.c**:
  - 注释了可能影响编译的模块初始化：串口、OLED、MPU6050、中断
  - 注释了复杂的任务：循迹、串口通信、OLED显示
  - 简化了电机PID控制任务，改为简单的速度测试
  - 注释了MPU6050相关的空闲任务代码

### 4. 主程序简化
- **main.c**:
  - 注释了任务系统，改为直接的电机测试循环
  - 实现了简单的电机正转-反转-停止测试序列
  - 每个状态持续3秒，停止状态持续2秒

## 测试功能
当前版本实现的功能：
1. 电机初始化
2. 左右电机同步正转（50%速度，3秒）
3. 左右电机同步反转（50%速度，3秒）
4. 电机停止（2秒）
5. 循环重复上述过程

## 注释的功能
为了专注电机测试，以下功能被暂时注释：
- 串口通信
- OLED显示
- MPU6050姿态传感器
- 循迹传感器
- 编码器中断
- PID闭环控制
- 任务调度系统

## 编译建议
1. 确保ti_msp_dl_config.h文件存在且配置正确
2. 确保PWM定时器(TIMG0)配置正确
3. 确保GPIO引脚配置正确：
   - PA12: 左电机PWM (TIMG0_CCP0)
   - PB6:  左电机方向控制
   - PA13: 右电机PWM (TIMG0_CCP1)  
   - PB25: 右电机方向控制

## 下一步调试
1. 编译并下载程序
2. 观察电机是否按预期转动
3. 如果电机不转动，检查：
   - PWM信号是否输出
   - 方向控制GPIO是否正确
   - 电机驱动电路是否正常
   - 电源是否充足

## 恢复完整功能
要恢复完整功能，需要：
1. 取消main.c中的注释，恢复任务系统
2. 取消Task_App.c中各模块的注释
3. 确保所有传感器和外设的硬件连接正确
