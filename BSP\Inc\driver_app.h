
#ifndef __MOTOR_DRIVER_H__
#define __MOTOR_DRIVER_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "ti_msp_dl_config.h"

/* Exported types ------------------------------------------------------------*/

/**
 * @brief 电机状态枚举
 */
typedef enum {
    MOTOR_STATE_STOP = 0,     // 停止
    MOTOR_STATE_FORWARD,      // 正转
    MOTOR_STATE_BACKWARD,     // 反转
    MOTOR_STATE_ERROR         // 错误
} MotorState_t;

/**
 * @brief 电机硬件配置结构体
 */
typedef struct {
    GPTIMER_Regs* timer_inst;       // 定时器实例
    DL_TIMER_CC_INDEX cc_index;     // 比较通道索引
    GPIO_Regs* dir_port;            // 方向控制GPIO端口
    uint32_t dir_pin;               // 方向控制GPIO引脚
} MotorHW_t;

/**
 * @brief 电机驱动实体结构体
 */
typedef struct {
    MotorHW_t hw;                   // 硬件配置
    int8_t speed;                   // 当前速度 (-100 到 +100)
    MotorState_t state;             // 当前状态
    uint8_t enable;                 // 使能标志
    uint8_t reverse;                // 电机安装方向 (0-正装, 1-反装)
} Motor_t;

// 为兼容原代码，定义MOTOR_Def_t结构体
typedef struct {
    PID_IQ_Def_t Motor_PID_Instance; // PID控制实例
    int16_t *Motor_Encoder_Addr;     // 编码器地址指针
    Motor_t *motor_hw;               // 硬件电机指针
} MOTOR_Def_t;

/* Exported constants --------------------------------------------------------*/
#define MOTOR_SPEED_MAX         100      // 最大速度
#define MOTOR_SPEED_MIN         -100     // 最小速度
#define MOTOR_PWM_PERIOD        100     // PWM周期值
#define MOTOR_MIN_PWM_THRESHOLD 10      // 最小PWM阈值(10%)
/* Exported macros -----------------------------------------------------------*/

/* Exported functions prototypes ---------------------------------------------*/

void Motor_Init(void);
void Motor_Start(void); // 兼容原代码的电机启动函数

// 兼容原代码的电机实例声明
extern MOTOR_Def_t Motor_Font_Left;   // 左前电机
extern MOTOR_Def_t Motor_Font_Right;  // 右前电机
extern MOTOR_Def_t Motor_Back_Left;   // 左后电机
extern MOTOR_Def_t Motor_Back_Right;  // 右后电机

// 兼容原代码的函数声明
void Motor_GetSpeed(MOTOR_Def_t *motor, uint16_t dt_ms); // 获取电机速度
void Motor_SetDuty(MOTOR_Def_t *motor, float duty);      // 设置电机占空比

/**
 * @brief 创建电机实体
 * @param motor: 电机实体指针
 * @param timer_inst: 定时器实例
 * @param cc_index: 比较通道索引
 * @param dir_port: 方向控制GPIO端口
 * @param dir_pin: 方向控制GPIO引脚
 * @param reverse: 电机安装方向 (0-正装, 1-反装)
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Motor_Create(Motor_t* motor,
                    GPTIMER_Regs* timer_inst,
                    DL_TIMER_CC_INDEX cc_index,
                    GPIO_Regs* dir_port,
                    uint32_t dir_pin,
                    uint8_t reverse);

/**
 * @brief 设置电机速度
 * @param motor: 电机实体指针
 * @param speed: 速度值 (-100 到 +100)
 *               正数为正转，负数为反转，0为停止
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Motor_SetSpeed(Motor_t* motor, int8_t speed);

/**
 * @brief 停止电机
 * @param motor: 电机实体指针
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Motor_Stop(Motor_t* motor);


/**
 * @brief 获取电机状态
 * @param motor: 电机实体指针
 * @retval 电机状态
 */
MotorState_t Motor_GetState(Motor_t* motor);

/**
 * @brief 使能/失能电机
 * @param motor: 电机实体指针
 * @param enable: 1-使能, 0-失能
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Motor_Enable(Motor_t* motor, uint8_t enable);

//电机刹车
void Motor_brake(Motor_t *motor);

#ifdef __cplusplus
}
#endif

#endif /* __MOTOR_DRIVER_H__ */

